---
title: Progress 进度条
order: 13
---

# Progress 进度条

## 何时使用

在操作需要较长时间才能完成时，为用户显示该操作的当前进度和状态。

- 操作在后台运行，需要耗费一定的客户端等待时间。
- 操作需要展示一个完成进度的百分比。

## 组件示例

import Iframe from 'components/Iframe';

<Iframe url="https://www.aliwork.com/developer/progress-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'size',
      type: `'small' | 'medium' | 'large'`,
      default: `'medium'`,
      desc: '尺寸',
    },
    {
      code: 'shape',
      type: `'circle' | 'line'`,
      default: `'line'`,
      desc: '进度条形态',
    },
    {
      code: 'percent',
      type: 'number',
      default: '0',
      desc: '设置百分比',
    },
    {
      code: 'state',
      type: `'normal' | 'success' | 'error'`,
      default: `'normal'`,
      desc: '当前状态',
    },
    {
      code: 'hasBorder',
      type: 'boolean',
      default: 'false',
      desc: '是否显示边框',
    },
    {
      code: 'progressive',
      type: 'boolean',
      default: 'false',
      desc: '设置色彩阶段变化模式',
    },
  ]}
/>