/**
 * CSS files with the .module.css suffix will be treated as CSS modules
 * and scoped locally.
 */

.heroBanner {
  padding: 3rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero__title{
  font-size: 2rem;
}

@media screen and (max-width: 966px) {
  .heroBanner {
    padding: 1rem;
  }
}

.buttons {
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  text-align: center;
  margin: 20px 0;
}
