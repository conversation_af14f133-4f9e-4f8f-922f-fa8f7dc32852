# 宜搭词汇表
本文介绍你可以在使用宜搭的过程中遇到的一些特殊词汇的概念，帮助你快速上手宜搭。

## **工作台**
| **名词** | **含义** |
| --- | --- |
| [开始](https://www.aliwork.com/start.html) | 登录宜搭后点击工作台首先跳转的就是开始页面，这个页面是一个大集合，您可以在这页上看到很多重要的信息概览。 |
| [模板中心](https://docs.aliwork.com/docs/yida_support/le1tkr) | 根据模板中心提供的模板，你可以了解到宜搭具体可以实现什么样的功能，能做到什么样的效果。 |
| [解决方案](https://www.aliwork.com/isvSolution.html?spm=a1ztuy.********.0.0.1a3a6ddbbEe5HW) | 由宜搭优秀认证服务商为您带来的行业化解决方案，并能根据您的需求提供灵活、高效的贴身定制服务，让最终交付的方案能够完全贴合您的业务。 |
| [应用中心](https://www.aliwork.com/appCenter.html?spm=a1z8hbp.20990513.0.0.10e13f7cNiOMcN) | 主要是展示在宜搭里面进行上线使用的应用或者启用的模板应用。 |
| [我的应用](https://docs.aliwork.com/docs/yida_support/ymyz8m) | 我的应用展示的是当前登录人员查看到的界面，不同权限成员展示的界面不一样。 |
| [任务中心](https://docs.aliwork.com/docs/yida_support/wpuat3) | 任务中心是宜搭应用中，您提交的表单或者流程数据的汇总，可以直接看见您提交的表单详情，和您需要处理的流程。 |
| [定制中心](https://docs.aliwork.com/docs/yida_support/mg2tlf) | 由认证服务商根据企业的业务需求，为企业量身定制业务应用，为了提供更多更优质的服务，我们在宜搭的开始页面增加了定制的入口。 |
| [帮助中心](https://www.yuque.com/yida/?spm=a1ztuy.********.0.0.1a3a6ddb72Z7Ry) | 宜搭的使用手册，包含有视频、案例等详细文档。 |
| [版本](https://docs.aliwork.com/docs/yida_support/iif7ge) | 2021 年 1 月 7 日之后开通的宜搭，页面会展示您的宜搭版本是免费版、轻享版或专业版。 |
| [平台管理](https://www.aliwork.com/accountManager.html#/role?_k=kr8npc) | 该入口是针对平台管理员权限、消息模板、角色、接口人等配置的入口。 |

## **普通表单**
| **名词** | **含义** |
| --- | --- |
| [普通表单](https://docs.aliwork.com/docs/yida_support/mff60m) | 普通表单由多个组件组成的，用于数据填报和收集的工具。 |
| [组件](https://docs.aliwork.com/docs/yida_support/snftao) | 数据存储的容器，不同的组件类型可以存储不同的数据类型。例如数值组件可以存储数值类型的数据，文本类组件可以存储文本数据。 |
| [公式编辑](https://docs.aliwork.com/docs/yida_support/gvtpe4) | 在填写表单或修改表单数据时，可以让组件的值根据公式自动计算得出，不需要手动填写，提高填写效率，减少填写错误。 |
| [关联其他表单数据](https://docs.aliwork.com/docs/yida_support/tg64d5) | 通过关联设置，去获取到被关联的表单的一个数据作为当前表单的数据。 |
| [数据联动](https://docs.aliwork.com/docs/yida_support/arpur0) | 当表单中某个字段的数据改变时，该表单中另一个字段的数据也会随之改变，常用于设置组件的默认值。 |
| [业务关联规则](https://docs.aliwork.com/docs/yida_support/ydmx04) | 当两张表单的数据进行关联的时候可以使用业务关联规则，例如我们的进销存，物资入库、出库都要统计到最终的库存表去进行展示查看。 |

## **流程表单**
| **名词** | **含义** |
| --- | --- |
| [流程表单](https://docs.aliwork.com/docs/yida_support/crwfii) | 带有流程的表单，相比普通表单，流程表单多了流程流转的环节。 |
| [流程设置](https://docs.aliwork.com/docs/yida_support/crwfii) | 为流程表单中的数据设置流转规则，使得数据可以按照预设的流程流转。 |
| [流程节点](https://docs.aliwork.com/docs/yida_support/rq8i94) | 流程流转的各个环节成为流程的节点，每一个节点都可以设置组件的可见、可操作等权限，以及流程审批人、执行人、抄送人。 |
| [节点提交规则](https://docs.aliwork.com/docs/yida_support/emt041) | 节点提交规则的作用是在用户提交流程或者审批人处理流程时通过一些公式校验判断用户是否能执行此操作。 |

## **报表**
| **名词** | **含义** |
| --- | --- |
| [报表设计器](https://docs.aliwork.com/docs/yida_support/nuk15o) | 需要将员工提交的数据做一个汇总进行查看，就可以设置一个报表页面，然后使用报表设计器里面的组件进行数据分析、汇总、查询等。 |
| [报表组件](https://docs.aliwork.com/docs/yida_support/yxgxg6) | 将表单里提交的数据进行图表展示。 |
| [报表公式](https://docs.aliwork.com/docs/yida_support/yrofmw) | 用于处理原始表单中的数据。 |
| [数据集](https://docs.aliwork.com/docs/yida_support/roe3o1) | 报表图表组件展示数据时，数据集选择就需要选择应用里面的表单、流程表单名称。 |
| [视图表](https://docs.aliwork.com/docs/yida_support/oim554) | 视图表通过单表或多表关联，将已有的表单数据进行预处理，同时也支持中间表模式，提升数据加工处理能力。目前，报表数据可视化、表单联动等场景可直接调用视图表中的数据。 |
| [跨应用](https://docs.aliwork.com/docs/yida_support/btrtnt) | 通常也叫【跨应用取数】，即在本应用内调用其他宜搭应用的表单数据，用于宜搭应用间的数据互联。 |

## **外部链接**
| **名词** | **含义** |
| --- | --- |
| [外部链接](https://docs.aliwork.com/docs/yida_support/nolqql#vM1iQ) | 当您需要展示其他网站内容时，可以复制网站的链接到这里。 |
| [新开页面](https://docs.aliwork.com/docs/yida_support/nolqql#C7boN) | 点击当前外部链接的时候，在新窗口打开。 |

## **自定义页面**
| **名词** | **含义** |
| --- | --- |
| [自定义页面](https://docs.aliwork.com/docs/yida_support/ynk2lo) | 可自定义的设置样式风格、展示应用所有表单的数据，更灵活的实现自己的场景需求，但比普通的表单设计较为复杂些，基本上都需要用代码配置。 |
| [自定义页面设计器](https://docs.aliwork.com/docs/yida_support/lo24cx) | 自定义页面设计器整体分为**顶部操作栏**、**左侧工具栏**、**中间画布**、**右侧属性配置面板**四个部分。 |

## **高级功能**
| **名称** | **含义** |
| --- | --- |
| [流水号](https://docs.aliwork.com/docs/yida_support/rom39o) | 能实现在提交表单数据后自动生成一串唯一性的编号，库存管理中给物品名称编号场景使用较多。 |
| [数据准备](https://docs.aliwork.com/docs/yida_support/dkm8ph) | 数据准备是指在可视化分析之前，需要对数据源和数据集进行一系列的处理，例如多表插入、数据转换等，是可视化分析的前序环节。 |
| [逻辑编排](https://docs.aliwork.com/docs/yida_support/lbvx0y) | 在某些场景下，表单在提交后会触发一些业务逻辑，目前宜搭支持了两种主要的内置核心业务逻辑流，分别是单据和流程，以及评论等次要业务逻辑流。<br />每种业务逻辑流有各自的生命周期，生命周期的各个阶段将抛出事件，可作为扩展点，事件被接受后可以触发对应的执行插件进行业务逻辑的执行。 |
| [高级流程](https://docs.aliwork.com/docs/yida_support/lgrp0w) | 为满足流程需求，宜搭提供了高级的流程设计器，它不仅能满足了普通设计器的几乎所有功能，还在此基础上增强了部分功能。例如增加了**分支**、**聚合**等组件。同时也对组件功能及设置进行了增强，区分基础和高级设置。可以自定义执行人规则和完成策略。支持普通流程编辑器升级为高级流程编辑器 |
| [服务回调](https://docs.aliwork.com/docs/yida_support/kkcdu6) | 在宜搭中，凡是涉及到接口服务调用的均称为第三方服务回调，通过对服务调用的支持，使宜搭可以和外部系统进行集成，数据互相流转，避免宜搭应用成为信息孤岛。 |
| [应用分发](https://docs.aliwork.com/docs/yida_support/xb5ylm) | 应用分发是为了帮助服务商快速地将当前企业下开发的应用，部署到客户企业的宜搭租户下，并且服务商开发人员可以通过专门的通道进入到客户企业下，完成一些定制化功能的开发和属性配置。 |
| [服务注册](https://docs.aliwork.com/docs/yida_support/ld80ev) | 服务注册是使用服务回调功能的前置步骤，即将需要使用到的第三方服务的接口注册到宜搭平台上来，主要的功能在于设置接口的出入参信息、验权加密信息、权限控制、接口描述信息等，以便在使用时可以快速引用。 |
| [集成&自动化](https://docs.aliwork.com/docs/yida_support/zevvr1) | 对逻辑编排功能进行了整体升级，同时接入了钉钉连接器，钉钉官方应用、钉钉生态内应用、企业自有系统可轻量化的接入宜搭，使得宜搭应用天然具有互联互通的能力。 |
| [连接器工厂](https://docs.aliwork.com/docs/yida_support/zevvr1#hIqTE) | 通过宜搭连接器工厂进行自定义连接自建系统或者第三方应用系统。 |
| [关联组织](https://docs.aliwork.com/docs/yida_support/ampson) | 企业通过钉钉的**关联组织**功能，将下级组织与上级组织建立关联，关联的时候，选择全部或部门下级组织的成员加入关联组织。 |
| [DataV大屏](https://docs.aliwork.com/docs/yida_support/vvgk1m) | 无缝使用各类已沉淀的数据集（含跨应用、子表、视图表等），制作炫酷数字化大屏，让你的宜搭应用，实现令人夺目的数字化表现力。 |
| **Corpid** | CorpId是钉钉的组织在钉钉的唯一识别码，购买服务、调用接口等等都需要这个 CorpId ，形如：dingfa087xxxx55b。你可以参考以下两种方式获取CorpId：<br />- 方式一：在宜搭平台设置页查看，操作步骤如下：依次进入**宜搭首页** > **平台设置** >  [基本信息](https://www.aliwork.com/platformManage/basicInfo)。<br />- 方式二：在钉钉开放平台开发者后台首页查看，详情可参考[钉钉开放平台文档](https://open.dingtalk.com/document/orgapp/basic-concepts#title-wxx-amg-62k)。<br /> |

