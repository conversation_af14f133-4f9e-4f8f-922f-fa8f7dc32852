---
title: RadioField 单选
order: 2
---

# RadioField 单选

## 何时使用

- 单选框允许用户从一个数据集中选择单个选项。面向用户需要并排看到所有的可选项，并使用单选框进行排他操作的场景。
- 对于选项过多的场景，考虑使用下拉列表，相对于显示所有的选项占用更少的空间。

## 组件示例

import Iframe from 'components/Iframe';

<Iframe url="https://www.aliwork.com/developer/radio-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  category="form"
  dataSource={[
    {
      code: 'value',
      type: 'string',
      default: `'选项一'`,
      desc: '默认值',
    },
    {
      code: 'shape',
      type: `'default' | 'button'`,
      default: `'default'`,
      desc: '展示形状',
    },
    {
      code: 'itemDirection',
      type: `'hoz' | 'ver'`,
      default: `'hoz'`,
      desc: 'PC端选项排列方式',
    },
    {
      code: 'useDrawerInMobile',
      type: 'boolean',
      default: 'false',
      desc: '手机端排列方式，为true则直接平铺，为false则底部弹层',
    },
    {
      code: 'iconPosition',
      type: `'left' | 'right'`,
      default: `'left'`,
      desc: '手机端Icon位置，仅手机端生效',
    },
    {
      code: 'dataSource',
      type: '[DataSource[]](/docs/components/interface#datasource)',
      default: `~~~json
[
  {
    "text": "选项一",
    "value": "1"
  },
  {
    "text": "选项二",
    "value": "2"
  },
  {
    "text": "选项三",
    "value": "3"
  }
]
      `,
      desc: '待选项',
    },
    {
      code: 'supportInverse',
      type: 'boolean',
      default: 'false',
      desc: '是否支持反选，再次点击选项可取消选择',
    },
    {
      code: 'customRender',
      type: '(item: DataSource) => ReactNode',
      default: '-',
      desc: '定制渲染',
    },
    {
      code: 'onChange',
      type: '(value: string) => void',
      default: '-',
      desc: '组件值发生改变事件',
    },
  ]}
/>
