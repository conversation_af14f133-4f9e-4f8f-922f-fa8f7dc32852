# 设计器功能介绍
本文档主要目的是让用户了解宜搭自定义页面设计器的总体框架及具体功能模块，帮助用户快速使用宜搭自定义设计器进行业务开发。

## 总体框架
宜搭自定义设计器总体框架包含五个功能区域，如下所示：

![](https://img.alicdn.com/imgextra/i3/O1CN01mzEfjo1dxoyPBPVj3_!!6000000003803-2-tps-3582-2016.png)

自定义页面设计器和表单设计器非常类似，主要包含五个区域：
* **顶部导航栏**- 除了提供宜搭的通用导航能力，同时也实现了页面名称快速编辑、页面保存及预览等功能；
* **左侧工具栏**- 主要用于页面全局配置，提供大纲树、组件库相关面板及一些其他全局配置面板，例如数据源管理、动作面板等；
* **顶部工具栏**- 主要用于可视编辑区配置，例如国际化语言切换、PC&手机端展示切换、历史记录等等；
* **可视编辑区**- 设计器的主体操作区，所见即所得地展示用户搭建页面，提供拖拽，点击等多种交互方式辅助用户进行页面搭建；
* **右侧工具栏**- 主要用于选中组件的属性配置，分为属性、样式、高级三个面板分别对应组件不同维度的属性配置；

## 功能详解
![](https://img.alicdn.com/imgextra/i1/O1CN019H2iC728OKMxr06Xg_!!6000000007922-2-tps-2580-1556.png)

上图中标记了自定义设计器的所有功能模块，接下来我们将逐一进行介绍：
### 1. 平台导航快速入口
鼠标 hover 到 Icon 图标便会下拉展示平台的快捷菜单栏，可以快速跳转到开始页、我的应用、应用中心等页面。

### 2. 应用名称
显示当前搭建页面所属的应用名称，点击该应用名称可以跳转到应用的页面管理页。

### 3. 页面名称
显示当前搭建页面的页面名称，点击小铅笔支持快捷编辑页面名称。

### 4. 页面设置导航
通过点击导航可以快速访问页面设置页和页面发布配置页。

### 5. 国际化语言切换
支持通过下拉方式切换不同的国际化语言，目前支持简体中文和英文。

### 6. 显示视窗切换
支持切换 PC 及手机视图模式，切换后可视化设计器会进行相应的适配展示。

### 7. 前进&后退
宜搭设计器会记录用户的操作记录，并支持前进后退操作。

### 8. 历史记录
点击历史记录 Icon，设计器会在右边显示近期用户保存的历史记录，用户可以选择某一个版本的历史记录进行回滚。

### 9. 全局搜索
宜搭设计器提供快速的全局搜索功能，用户可以通过关键词搜索并定位设置的具体位置。

### 10. 快捷键
点击快捷键 Icon 会唤起快捷键显示弹窗，用户可以查看宜搭设计器支持的快捷键列表。

### 11. 页面设置
点击页面设置按钮，右侧工具栏会切换到页面的设置面板，用户可以对整个页面的最外层容器进行属性配置及样式配置。

### 12. 预览
点击预览按钮，页面信息将自动保存，同时会在页面设计器中唤起预览浮窗进行页面真实预览。

### 13. 保存
将用户编辑的页面信息保存到服务端。

### 14. 大纲树
通过大纲树面板，用户可以清晰查看当前编辑页面的组件层级结构，在大纲树面板用户可以进行以下操作：
* 点击选中组件：当用户点击组件树中的节点时，画布中将高亮该节点对应的组件；
* 拖拽移动组件：用户可以拖拽组件改变组件位置，拖拽操作支持组件树&画布之间相互拖拽；
* 快捷隐藏组件：鼠标经过组件树节点时，用户可以看到快捷操作 icon，用户可以快速隐藏组件；

### 15. 组件库
用户可以查看当前页面可以使用的所有官方组件列表，用户可以通过名称搜索或者组件库筛选等操作快速查找需要使用的组件，同时用户可以操作拖拽组件库中的组件并添加到可视编辑区的指定位置。

### 16. 区块模板
区块是组件按照业务规则的组合，因此可以理解区块是相较于组件粒度更大的物料，区块库的使用和组件库类似，支持用户通过拖拽的方式将区块物料添加到画布或者组件树中。

### 17. 数据源
页面数据源配置，宜搭的自定义页面支持两类数据源：
* 变量 - 用于进行页面全局状态管理；
* 远程 API - 用于进行页面的异步数据请求管理；

### 18. 动作面板
用于通过 JS 代码书写业务逻辑，语法支持全量 ES6 语法，具体有以下几类配置场景：
* 页面生命周期配置；
* 动作处理函数配置；
* 页面公共函数配置；

### 19. 多语言文案管理
用于配置国际化的多语言文案库，同时提供 `i18n` API 进行语言库获取。

### 20. 高级属性配置
用于配置选中组件的高级属性，具体包含：
* 是否渲染：支持实现组件的动态条件渲染；
* 循环：支持配置实现组件的循环渲染；
* 唯一标识：组件的唯一标识，类似 React 中的 key 的概念；

### 21. 样式属性配置
用户可以选中画布中的组件，并在右侧样式面板中编辑选中组件的样式 (对应 React 组件的 style 属性)，所有组件的样式面板配置都是一致的，并且都有对应的 css 样式属性。

### 22. 组件属性配置
用户可以选中可视化编辑区域中的组件，并在右侧属性面板中编辑选中的组件属性 (对应 React 组件的 props)，设计器会根据组件接入时设置的属性配置展示不同的属性配置面板。

### 23. 反馈中心
用于快捷寻求支持或者咨询，具有以下快捷入口：
* 售前咨询；
* 定制开发；
* 关注公众号；
* 体验反馈；
* 人工服务；

### 24. 可视化编辑区域
可视化编辑的主体区域，用户可以在画布中进行如下操作：
* 点击选中组件：选中右侧配置面板需要配置的组件；
* 拖拽移动组件：组件树及画布的相互物料拖拽移动；
* 拖拽添加组件：从组件库及区块库选中物料实例拖拽添加到编辑区域；