---
title: Link 链接
order: 5
---

# Link 链接

跳转超链接，用于跳转到新页面。

## 何时使用

- 切换访问当前应用的其他页面；
- 跳转到外部页面。

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/link-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'content',
      type: 'string',
      default: `'这里是一个链接'`,
      desc: '置链接文本内容',
    },
    {
      code: 'textOverflow',
      type: 'boolean',
      default: 'false',
      desc: '文本单行截断，文字超过一行是否使用...显示，开启此项后，同时会设置html标签的title属性',
    },
    {
      code: 'type',
      type: `'page' | 'url'`,
      default: `'page'`,
      desc: '链接类型，page：内部页面、url：外部链接',
    },
    {
      code: 'url',
      type: 'string',
      default: '-',
      desc: '链接地址，点击链接即可跳转对应页面，当 **type** 属性为url时生效',
    },
    {
      code: 'isBlank',
      type: 'boolean',
      default: 'false',
      desc: '是否新开页面',
    },
    {
      code: 'params',
      type: 'Record<string, string>',
      default: '-',
      desc: 'url 查询参数，多用于页面跳转传参',
    },
    {
      code: 'page',
      type: 'string',
      default: '-',
      desc: '内部页面Uuid，当 **type** 属性为page时生效',
    },
  ]}
/>

