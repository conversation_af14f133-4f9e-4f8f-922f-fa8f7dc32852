module.exports = {
  style: 'light',
  links: [
    {
      title: '快捷入口',
      items: [
        {
          to: 'https://docs.aliwork.com/',
          label: '帮助中心',
        },
        {
          to: 'https://www.aliwork.com/pricing',
          label: '版本',
        },
        {
          to: 'https://developer.aliyun.com/group/yida',
          label: '社区',
        },
        {
          to: 'https://developers.aliwork.com/',
          label: '开发者中心',
        },
      ],
    },
    {
      title: '更多',
      items: [
        {
          to: 'https://www.aliwork.com/home',
          label: '关于宜搭',
        },
        {
          to: 'https://www.aliwork.com/o/team',
          label: '向团队推荐宜搭',
        },
        {
          to: 'https://docs.aliwork.com/docs/yida_updates',
          label: '更新日志',
        },
      ],
    },
    {
      title: '联系我们',
      items: [
        {
          to: 'https://partner.dingtalk.com/opportunity_web.html?templateId=3fe61c97b34b4fa2b1582c52690c1471&channel=%E5%94%AE%E5%89%8D%E5%92%A8%E8%AF%A2-%E5%B0%8F%E8%9C%9C#/consultingService',
          label: '售前咨询',
        },
        {
          to: 'https://docs.aliwork.com/docs/yida_support/bp2rhh',
          label: '技术支持',
        },
        {
          to: 'https://www.aliwork.com/o/aliwork_partner',
          label: '生态与伙伴',
        },
        {
          to: 'https://www.aliyun.com/product/yida',
          label: '宜搭阿里云官网',
        },
      ],
    },
  ],
  logo: {
    src: 'https://img.alicdn.com/imgextra/i2/O1CN01H3ajqP1bxF9chDQmu_!!6000000003531-55-tps-131-32.svg',
    href: 'https://www.aliwork.com/',
  },
};
