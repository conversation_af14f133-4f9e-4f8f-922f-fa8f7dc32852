---
title: BannerContainer 底部通栏
order: 4
---

# BannerContainer 底部通栏

底部通栏 组件允许你在页面底部添加内容，并且内容固定在页面底部和占据通栏。

## 何时使用

- 需要在页面底部添加内容时，例如添加版权信息等。

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/banner-container-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'autoWidth',
      type: 'boolean',
      default: 'false',
      desc: '是否开启自适应宽度',
    },
    {
      code: 'layout',
      type: `'h' | 'v'`,
      default: `'h'`,
      desc: '设置布局方式，h代表横向、v代表纵向',
    },
    {
      code: 'contentWidth',
      type: 'string',
      default: '-',
      desc: '设置内容宽度，可以用px或%，例如：100px、50%',
    },
    {
      code: 'containerWidth',
      type: 'string',
      default: '-',
      desc: '设置容器宽度，可以用px或%，例如：100px、50%',
    },
    {
      code: 'extra',
      type: 'ReactNode',
      default: '-',
      desc: '自定义内容',
    },
    {
      code: 'getRefContainer',
      type: '() => React.HTMLElement',
      default: '-',
      desc: '设置自定义挂载容器',
    },
  ]}
/>
