---
title: NumberField 数字输入框
order: 1
---

# NumberField 数字输入框

## 何时使用

- 数字选择器，并对输入的数据做正确性检查、自动订正；

## 组件示例

import Iframe from 'components/Iframe';

<Iframe url="https://www.aliwork.com/developer/number-field-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  category="form"
  dataSource={[
    {
      code: 'value',
      type: 'number',
      default: '-',
      desc: '表单组件的默认值',
    },
    {
      code: 'onChange',
      type: '({value: number}) => void',
      default: '-',
      desc: '表单组件值发生改变事件',
    },
    {
      code: 'placeholder',
      type: 'string',
      default: `'请输入数字'`,
      desc: '表单组件占位提示信息',
    },
    {
      code: 'innerBefore',
      type: 'string',
      default: '-',
      desc: '表单组件前缀文案',
    },
    {
      code: 'innerAfter',
      type: 'string',
      default: '-',
      desc: '数字输入框单位文案',
    },
    {
      code: 'thousandsSeparators',
      type: 'boolean',
      default: 'false',
      desc: '是否进行千分位分隔',
    },
    {
      code: 'precision',
      type: 'number',
      default: '0',
      desc: '小数点位数，取值范围在0 - 20',
    },
  ]}
/>