---
title: TabsLayout 选项卡
order: 1
---

# TabsLayout 选项卡

让用户可以在不同子任务、视图、模式之间切换，它具有全局导航的作用，是全局功能的主要展示和切换区域。


## 何时使用
- 当需要对页面内容进行分类或区隔时使用。
- 当需要对页面控件进行压缩，提升页面空间利用率时。


## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/tabs-layout-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'items',
      type: '[TabItem[]](/docs/components/interface#tabitem)',
      default: '-',
      desc: '可以设置默认激活选项、禁用选项和标签名',
    },
    {
      code: 'shape',
      type: `'pure' | 'wrapped' | 'text' | 'capsule'`,
      default: `'pure'`,
      desc: '设置选项卡形态',
    },
    {
      code: 'size',
      type: `'small' | 'medium'`,
      default: `'medium'`,
      desc: '标签项大小',
    },
    {
      code: 'contentPadding',
      type: `string`,
      default: `'20px 20px'`,
      desc: '设定内容区的内间距，请输入 CSS padding 值',
    },
    {
      code: 'excessMode',
      type: `'slide' | 'dropdown'`,
      default: `'slide'`,
      desc: '选项卡过多时的滑动模式，slide：滑动、dropdown：下拉选择',
    },
    {
      code: 'tabPosition',
      type: `'top' | 'bottom' | 'left' | 'right'`,
      default: `'top'`,
      desc: '导航选项卡的位置',
    },
    {
      code: 'needBadge',
      type: 'boolean',
      default: `false`,
      desc: '开启徽标',
    },
    {
      code: 'renderBadge',
      type: `(tabItem: object) => ReactNode`,
      default: `-`,
      desc: '徽标渲染',
    },
    {
      code: 'tabRender',
      type: `(key: string, props: object) => ReactNode`,
      default: `-`,
      desc: '自定义渲染选项卡',
    },
    {
      code: 'extraRender',
      type: `() => ReactNode`,
      default: `-`,
      desc: '渲染导航栏附加内容',
    },
    {
      code: 'onTabChange',
      type: '(activeIndex: number, key: string) => void',
      default: '-',
      desc: '选项卡切换事件',
    },
  ]}
/>