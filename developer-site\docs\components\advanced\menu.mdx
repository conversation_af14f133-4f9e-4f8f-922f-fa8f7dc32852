---
title: Menu 菜单
order: 11
---

# Menu 菜单

## 何时使用

- 导航菜单是一个网站的灵魂，用户依赖导航在各个页面中进行跳转。一般分为顶部导航和侧边导航，顶部导航提供全局性的类目和功能，侧边导航提供多级结构来收纳和排列网站架构。

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/menu-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'dataSource',
      type: '[MenuDataSource[]](/docs/components/interface#menudatasource)',
      default: `~~~json
[
  {
    label: "菜单项一",
    key: "key1",
      children: [
        {
          label: "选项一",
          key: "key1-1",
          tag: {
            text: "初始化",
            color: "blue",
          },
        },
        {
          label: "选项二",
          key: "key1-2",
        },
      ],
    },
    {
      isDivider: true,
    },
    {
      label: "菜单项二",
      key: "key2",
      children: [
        {
          label: "选项一",
          key: "key2-1",
        },
        {
          label: "选项二",
          key: "key2-2",
        },
      ],
    },
    {
      label: "菜单项三",
      key: "key3",
      disabled: true,
    },
  ]      
`,
      desc: '菜单的数据源',
    },
    {
      code: 'popupAlign',
      type: `'follow' | 'outside'`,
      default: `'follow'`,
      desc: '弹层的对齐方式',
    },
    {
      code: 'onItemClick',
      type: '(key: string,item: MenuDataSource, event: any) => void',
      default: '-',
      desc: '点击菜单项触发的事件',
    },
    {
      code: 'mode',
      type: `'inline' | 'popup'`,
      default: 'inline',
      desc: '设置子菜单打开的模式',
    },
    {
      code: 'triggerType',
      type: `'click' | 'hover'`,
      default: `'click'`,
      desc: '设置子菜单打开的触发行为',
    },
    {
      code: 'onSelect',
      type: '(selectedKeys: string[] ,extraObj: any) => void',
      default: '-',
      desc: '选中或取消选中菜单项时触发的事件',
    },
    {
      code: 'selectMode',
      type: `'single' | 'multiple' | false `,
      default: 'false',
      desc: '菜单组件的选择模式',
    },
    {
      code: 'direction',
      type: `'ver' | 'hoz'`,
      default: '-',
      desc: '菜单第一层展示方向',
    },
    {
      code: 'header',
      type: 'string',
      default: '-',
      desc: '自定义菜单头部',
    },
    {
      code: 'footer',
      type: 'string',
      default: '-',
      desc: '自定义菜单尾部',
    },
  ]}
/>