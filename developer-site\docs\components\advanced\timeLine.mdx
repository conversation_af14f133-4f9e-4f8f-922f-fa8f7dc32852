---
title: Timeline 时间轴
order: 14
---

# Timeline 时间轴

垂直展示的时间流信息。

## 何时使用

- 当有一系列信息需要从上至下按时间排列时。
- 需要有一条时间轴进行视觉上的串联时。

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/timeLine-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'dataSource',
      type: '[TimelineDataSource[]](/docs/components/interface#timelinedatasource)',
      default: `~~~json
[
  {
    title: {
      zh_CN: "春天",
      en_US: "Spring",
      type: "i18n",
    },
    time: "2021-03-10",
  },
  {
    title: {
      zh_CN: "夏天",
      en_US: "Summer",
      type: "i18n",
    },
    time: "2016-06-11",
  },
  {
    title: {
      zh_CN: "秋天",
      en_US: "Autumn",
      type: "i18n",
    },
    time: "2016-09-09",
  },
  {
    title: {
      zh_CN: "冬天",
      en_US: "Winter",
      type: "i18n",
    },
    time: "2016-12-08",
  },
]`,
      desc: '用于配置时间轴数据',
    },
    {
      code: 'fold',
      type: 'any[]',
      default: `~~~json 
[
  {
    foldArea: [1, 2],
    foldShow: false,
  },
]`,
      desc: '用于配置数据折叠',
    },
    {
      code: 'animation',
      type: 'boolean',
      default: 'true',
      desc: '是否开启动画',
    },
  ]}
/>
