---
title: LinkBlock 链接块
order: 6
---

# LinkBlock 链接块

链接块容器，功能同链接组件，不过可以嵌入其他内容，例如图片等。

## 何时使用

- 切换访问当前应用的其他页面；
- 跳转到外部页面。

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/link-block-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'type',
      type: `'page' | 'url'`,
      default: `'page'`,
      desc: '链接类型，page：内部页面、url：外部链接',
    },
    {
      code: 'url',
      type: 'string',
      default: '-',
      desc: '链接地址，点击链接即可跳转对应页面，当 **type** 属性为url时生效',
    },
    {
      code: 'isBlank',
      type: 'boolean',
      default: 'false',
      desc: '是否新开页面',
    },
    {
      code: 'disabled',
      type: 'boolean',
      default: 'false',
      desc: '是否禁用，禁用后点击链接块不会进行页面跳转',
    },
    {
      code: 'params',
      type: 'Record<string, string>',
      default: '-',
      desc: 'url 查询参数，多用于页面跳转传参',
    },
    {
      code: 'page',
      type: 'string',
      default: '-',
      desc: '内部页面Uuid，当 **type** 属性为page时生效',
    },
  ]}
/>