---
title: <PERSON><PERSON>ield 富文本编辑
order: 14
---

# Editor<PERSON><PERSON> 富文本编辑

## 何时使用

- 当用户需要对文本进行复杂排版时使用；
- 当用户需要进行图文混合展示时使用；

## 组件示例

import Iframe from 'components/Iframe';

<Iframe url="https://www.aliwork.com/developer/editor-field-v2?isRenderNav=false" />

## 组件属性


import AttrTable from 'components/AttrTable';

<AttrTable
  category="form"
  dataSource={[
    {
      code: 'value',
      type: 'string',
      default: '-',
      desc: '富文本默认值',
    },
    {
      code: 'placeholder',
      type: 'string',
      default: `'请输入'`,
      desc: '占位提示',
    },
    {
      code: 'config',
      type: 'object',
      default: `~~~json
{
  "statusbar": false,
  "menubar": false,
  "toolbar1": "undo redo bold italic underline strikethrough | fontselect fontsizeselect forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | table link image media upload | code | fullscreen | variable | removeformat",
  "toolbar2": false,
  "height": 300,
  "fontsize_formats": "8px 10px 12px 14px 16px 18px 24px 36px 48px",
  "visual": true,
  "keep_values": false,
  "forced_root_block": "div",
  "plugins": [
    "advlist autolink lists link image charmap print preview anchor",
    "searchreplace visualblocks code fullscreen",
    "insertdatetime media table contextmenu paste code",
    "colorpicker",
    "upload",
    "placeholder",
    "variable",
    "noneditable"
  ]
} {
  "statusbar": false,
  "menubar": false,
  "toolbar1": "undo redo bold italic underline strikethrough | fontselect fontsizeselect forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | table link image media upload | code | fullscreen | variable | removeformat",
  "toolbar2": false,
  "height": 300,
  "fontsize_formats": "8px 10px 12px 14px 16px 18px 24px 36px 48px",
  "visual": true,
  "keep_values": false,
  "forced_root_block": "div",
  "plugins": [
    "advlist autolink lists link image charmap print preview anchor",
    "searchreplace visualblocks code fullscreen",
    "insertdatetime media table contextmenu paste code",
    "colorpicker",
    "upload",
    "placeholder",
    "variable",
    "noneditable"
  ]
}
~~~`,
      desc: '通用配置',
    },
    {
      code: 'uploadConfig',
      type: '[UploadConfig](/docs/components/interface#uploadconfig)',
      default: '{}',
      desc: '图片上传配置',
    },
    {
      code: 'onChange',
      type: '({ value : string }) => void',
      default: '-',
      desc: 'onChange 值发生变化',
    },
  ]}
/>