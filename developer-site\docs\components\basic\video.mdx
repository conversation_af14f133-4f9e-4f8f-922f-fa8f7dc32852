---
title: Video 视频播放
order: 9
---

# Video 视频播放

## 何时使用

-视频控件，用于播放 mp4、mov 等 HTML 原生支持的视频资源。

## 组件示例

import Iframe from 'components/Iframe';

<Iframe url="https://www.aliwork.com/developer/video-v2?isRenderNav=false" />

## 组件属性
import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'title',
      type: 'string',
      default: '-',
      desc: '原生标题，用于设置html 原生 title属性，在用户鼠标hover时显示',
    },
    {
      code: 'url',
      type: 'string',
      default: ` 'https://cloud.video.taobao.com/play/u/1804320196/p/1/e/6/t/1/287344840104.mp4' `,
      desc: '视频URL地址',
    },
    {
      code: 'poster',
      type: 'string',
      default: '-',
      desc: '封面图片URL',
    },
    {
      code: 'sizeFit',
      type: `'contain' | 'fill' | 'cover' | 'scale-dowm' | 'none'`,
      default: ` 'contain' `,
      desc: '画面适配模式',
    },
    {
      code: 'controls',
      type: 'boolean',
      default: 'true',
      desc: '控制条',
    },
    {
      code: 'controlsList',
      type: `'nodownload' | 'nofullscreen' | 'noremoteplayback' | 'disablePictureInPicture'`,
      default: '-',
      desc: '定制原生控制条',
    },
    {
      code: 'playbackRateControls',
      type: 'boolean',
      default: 'false',
      desc: '是否显示倍数播放',
    },
    {
      code: 'playbackRate',
      type: 'array',
      default: `~~~json
[
  0.8,
  1,
  1.5,
  2
]`,
      desc: '倍数列表',
    },
    {
      code: 'muted',
      type: 'boolean',
      default: 'false',
      desc: '是否静音',
    },
    {
      code: 'volumeControls',
      type: 'boolean',
      default: 'true',
      desc: '是否显示声音控件',
    },
    {
      code: 'volume',
      type: 'number',
      default: '1',
      desc: '音量大小，最小为0，最大为1',
    },
    {
      code: 'autoplay',
      type: 'boolean',
      default: 'false',
      desc: '自动播放，可能在某些场景下无法起作用',
    },
    {
      code: 'loop',
      type: 'boolean',
      default: 'false',
      desc: '循环播放，播放完成后是否自动循环播放',
    },
    {
      code: 'pipControls',
      type: 'boolean',
      default: 'false',
      desc: '画中画，只在 chrome，safari 浏览器上生效，firefox 和手机端不生效',
    },
    {
      code: 'onPlay',
      type: '(ctx: object) => void',
      default: '-',
      desc: '当开始播放时',
    },
    {
      code: 'onPause',
      type: '(ctx: object) => void',
      default: '-',
      desc: '当视频暂停时',
    },
    {
      code: 'onEnded',
      type: '(ctx: object) => void',
      default: '-',
      desc: '当视频播放结束时',
    },
    {
      code: 'onEnterFullscreen',
      type: '(ctx: object) => void',
      default: '-',
      desc: '当全屏时',
    },
    {
      code: 'onExitFullscreen',
      type: '(ctx: object) => void',
      default: '-',
      desc: '当退出全屏时',
    },
    {
      code: 'onError',
      type: '(ctx: object) => void',
      default: '-',
      desc: '当播放出错时',
    },
    {
      code: 'timeUpdate',
      type: '(ctx: object) => void',
      default: '-',
      desc: '当播放时间更新时',
    },
    {
      code: 'canPlayThrough',
      type: '(ctx: object) => void',
      default: '-',
      desc: '当加载完成可以播放时',
    },
  ]}
/>
