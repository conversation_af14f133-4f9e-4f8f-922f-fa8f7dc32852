---
title: 如何调用表单数据作为数据源？
order: 1
---

# 如何调用表单数据作为数据源？

## 使用场景

在自定义页面如何将表单/流程页面中的数据获取，并进行展示，可参考本文档配置获取商品信息展示在自定义页面。


## 视频教程
<video width="100%" controls>
  <source src="https://cloud.video.taobao.com/play/u/null/p/1/e/6/t/1/d/ud/365815370912.mp4" type="video/mp4"></source>
</video>


## 操作步骤

### 步骤 1：获取表单已经提交的数据

用户可以通过访问[宜搭服务 OpenAPI](/docs/api/openAPI)查看宜搭提供的所有远程 OpenAPI，从中可以找到表单数据查询接口。

### 步骤 2：配置数据源

- 添加数据源 - 按照 OpenAPI 的文档拼接接口并进行请求地址配置，例如：`/dingtalk/web/APP_M14MKG7F402M1Q7H2MGO/v1/form/searchFormDatas.json`;

![](https://img.alicdn.com/imgextra/i1/O1CN01F8mIP31hyW2cfj6Pk_!!6000000004346-2-tps-1700-999.png_.webp)

- 配置参数 - 将获取表单数据的 formUuid 填充到请求参数上；

![](https://img.alicdn.com/imgextra/i2/O1CN01FQH30Z1yvd0isOWG8_!!6000000006641-2-tps-1908-881.png)

- 获取 formUuid - 通过一下页面便可以查看当前应用的所有表单的 formUuid；

![](https://img.alicdn.com/imgextra/i4/O1CN01xAV9f01GnzJIsRXGI_!!6000000000668-2-tps-1700-785.png)

- 保存数据源并查看效果；

## 效果展示

用户可以访问官方提供的[表单/流程数据用于展示数据源示例](https://docs.aliwork.com/docs/yida_subject/_1/cuqxlstcxcqr5331)来查看效果及具体实现，最终展示效果如下所示：

![](https://img.alicdn.com/imgextra/i3/O1CN01gQULBi1vJCwOipfZ4_!!6000000006151-2-tps-1012-458.png)
