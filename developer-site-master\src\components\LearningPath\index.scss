.learning-path {
  margin: 0 auto 100px;
  padding: 20px;
  font-family: var(--ifm-heading-font-family);
  font-weight: var(--ifm-heading-font-weight);
  color: var(--ifm-heading-color);
  .ant-steps {
    padding: 20px 100px;
  }
  .ant-steps-item-title {
    display: flex;
    .step-title {
      display: inline-block;
      width: 160px;
      line-height: 36px;
      font-size: 18px;
      color: var(--ifm-font-color-base);
    }
    .ant-steps-item-subtitle {
      flex: 1;
      .step-sub-title {
        font-size: 16px;
        font-weight: 500;
        line-height: 36px;
        color: var(--ifm-font-color-base);
      }
      .path-block {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 20px;
        .path-item {
          width: 20%;
          font-weight: 400;
          font-size: 14px;
        }
      }
      
    }
  }
}

@media screen and (max-width: 966px) {
  .learning-path {
    .ant-steps {
      padding: 0px;
    }
    .ant-steps-item-title {
      flex-direction: column;
      .ant-steps-item-subtitle {
        .path-block .path-item {
          width: 100% !important;
        }
      }
    }
  }
}
