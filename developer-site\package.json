{"name": "yida-developer", "version": "0.1.0", "description": "宜搭开发者中心", "dependencies": {"@aldridged/docusaurus-plugin-lunr": "^1.0.0-alpha.10", "@ant-design/icons": "^4.8.0", "@docusaurus/core": "^2.0.0-beta.17", "@docusaurus/preset-classic": "^2.0.0-beta.17", "@mdx-js/react": "^1.6.21", "antd": "^4.19.2", "axios": "^0.26.1", "babel-plugin-import": "^1.13.3", "clsx": "^1.1.1", "core-js": "^3.23.5", "docusaurus-plugin-sass": "^0.2.1", "gh-pages": "^3.2.3", "plugin-image-zoom": "^1.1.0", "prism-react-renderer": "^1.2.1", "react": "^17.0.1", "react-dom": "^17.0.1", "react-markdown": "^8.0.0", "remark-gfm": "^3.0.1"}, "devDependencies": {"@docusaurus/module-type-aliases": "^2.0.0-beta.17", "@easyops-cn/docusaurus-search-local": "^0.21.4", "@tsconfig/docusaurus": "^1.0.4", "docusaurus-plugin-hotjar": "^0.0.2", "eslint": "^8.8.0", "gh-pages": "^3.2.3", "glob": "^7.2.0", "gray-matter": "^4.0.3", "husky": "^7.0.4", "lint-staged": "^12.3.3", "nodejieba": "2.5.2", "sass": "^1.49.7", "stylelint": "^14.4.0", "typescript": "^4.5.2"}, "resolutions": {"joi": "17.13.1", "@docusaurus/plugin-content-docs": "2.4.3", "@docusaurus/core": "2.2.0"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus build && gh-pages -d ./build -b site", "deploy:gitee": "docusaurus build --config ./docusaurus.gitee.config.js && gh-pages -d ./build -b site -o gitee", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc"}, "repository": {"type": "git", "url": "https://github.com/dingtalk-yida/developer-site"}, "private": true, "originTemplate": "./public/index.html", "lint-staged": {"*{.ts,.js,.tsx,.jsx}": "eslint", "*.css": "stylelint", "*.scss": "stylelint --syntax=scss", "*.less": "stylelint --syntax=less"}}