---
title: 组件管理
order: 2
---

## 页面介绍

**组件管理**页面，用于查看、安装自定义组件，分为**「我创建的组件」**、**「当前组织的组件」**以及**「组件市场」（暂未开放，敬请期待~）**三个模块，对组件从不同维度进行展示，便于使用者对组件的管理。

### 页面入口

**路径：**应用后台 >> 应用设置 >> 组件管理。（如下图所示）
![](https://img.alicdn.com/imgextra/i3/O1CN01pLawT21aedXljrXkY_!!6000000003355-2-tps-959-531.png_.webp)

### 页面功能详述

**组件管理表**
页面分**「我创建的组件」**、**「当前组织的组件」**以及**「组件市场」（暂未开放，敬请期待~）**三个模块，以表格的形式进行上述三个维度的信息展示。（如下图所示）
![](https://img.alicdn.com/imgextra/i3/O1CN01ti0Mtl28W76sdhiCF_!!6000000007939-2-tps-750-382.png_.webp)

其中，各个维度的展示内容为：

- **我创建的组件：**展示**当前登录人**所创建的组件信息。
- **当前组织的组件：**展示**当前组织内所有成员**创建的组件信息。
- **组件市场：**暂未开放，敬请期待~

#### 组件信息表中各项信息说明：
| **项目名称** | **含义** |  
| --- | --- | 
| 组件名称 | 组件创建时所填写的组件名称，一经创建不可修改。 |  
| 组件描述 | 组件创建时所填写的组件概括性信息。后续可以[进行修改](/docs/guide/customComponent/start#步骤5查看修改组件信息)。 |  
| 最新版本 | 当前组件开发迭代的最高版本 | 
| 已安装版本 | 当前可以在表单设计器中进行使用的版本 |  
| 类型 | 当前组件的类型，[点击查看](/docs/guide/concept/customComponent#组件类型)组件类型说明。 |  
| 安装范围 | 当前组件安装范围，决定组件在页面内是可以使用，[点击查看](/docs/guide/concept/customComponent#组件安装类型)安装范围说明。 |  
| 更新时间 | 组件当前版本最后的发布时间。 |  
| 管理员 | 拥有对当前组件进行开发调试的权限，默认为组件创建者，可以对[管理员进行增添或删除](/docs/guide/customComponent/start#步骤5查看修改组件信息)。 |  
| 操作-安装 | 对当前组件进行安装操作，注意[组件版本](/docs/guide/concept/customComponent#组件版本说明)及组件[安装范围](/docs/guide/concept/customComponent#组件安装类型)的选择。 |  
| 操作-查看文档 | 点击查看当前组件的说明文档，在组件创建或组件基本信息中以文档链接的形式进行填入，便于组件使用者进行了解。 |  

**组件中心**
点击页面页面右上角**组件中心**按钮，即可进入组件中心。（如下图所示）
![](https://img.alicdn.com/imgextra/i3/O1CN01JMx3xA1mH4YRcCMAo_!!6000000004928-2-tps-959-531.png_.webp)

其中：

- **新增组件**按钮：可以进行[自定义组件的创建](/docs/guide/customComponent/start#步骤1创建自定义组件)。
- **我的组件：**与[组件管理表](/docs/guide/customComponent/manage#组件信息表中各项信息说明)不同的是，我的组件页面表格仅展示由当前登录人所创建的且已经发布安装的组件信息，点击「开发」按钮，可以对[组件进行开发调试](/docs/guide/customComponent/debug)，点击「详情」，可以[查看或修改组件信息](/docs/guide/customComponent/start#步骤5查看修改组件信息)。

:::info
**说明：**我的组件表格所展示的各项信息含义与[上表](/docs/guide/customComponent/manage#组件信息表中各项信息说明)相同。
:::
