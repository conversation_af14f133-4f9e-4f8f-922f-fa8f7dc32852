# 宜搭数据安全

### Q：宜搭有哪些被认证的安全保障 ？
宜搭基于阿里云及钉钉的安全底座，具备完整的等级认证，同时宜搭已经通过 ISO27001、ISO27018 信息安全管理体系等认证。

![宜搭数据安全](https://yida-support.oss-cn-shanghai.aliyuncs.com/static/jpeg/1701748359918-28dd395e-db54-4c94-902c-7b35449f5eb6.jpeg)

### Q：宜搭如何在底层架构上保障数据安全 ？

- 编码严格遵守阿里巴巴开发规约和安全规约。
- 完全部署在阿里云上，网络、计算、存储完全使用阿里云技术体系，平台底座使用阿里 JDK 开发和运行。
- 所有 WEB 请求使用 HTTPS 协议加密。
- 密码本身不会输出到日志和控制台。
- 除了用户手动设定为“公开”的内容外，所有网页和资源均要求进行身份验证。身份验证完全在服务端进行，登录入口具备了防暴力猜解及撞库猜解的措施，并且支持第三方授权登录。
- 用户登出后会立即清理会话以及相关登录信息，COOKIE 设置 HTTPONLY 和 SECURE 属性。
- 管理员用户可以根据需求，自行配置权限，实现对应资源的访问控制。
- WEB 防护措施。包括了防止 SQL 注入、XML 注入、缓冲区溢出、XSS 跨站脚本攻击、CSRF 跨站请求伪造等。
- 文件上传对上传者进行身份验证，单独使用 OSS 文件服务器存储，文件服务器独立域名。
- 开放接口调用严格控制接入方 Key 和 Secret，提供数据签名机制，对调用频率、异常调用均有拦截，HTTPS 加密传输。

### Q：宜搭有哪些产品层面的数据安全保障 ？
- 采用 SAAS 方式提供服务，设计上采用多租户模式，租户间数据逻辑隔离，租户信息不能访问共享 。
- 严格的数据权限配置管控能力。
- 基于钉钉安全账户体系（支持实名认证）。
- 具备统一的数据管理界面。

### Q：宜搭的云端数据储存，使用了安恒密盾加密吗？
A：没有使用安恒密盾，但宜搭使用阿里自研的安全数据库进行数据保证，数据传输全程使用加密通道传送。

### Q：宜搭是否通过了三级等保认证？用宜搭搭建出来的应用，是否自动具备三级等保认证。
A: 宜搭是钉钉下的官方应用，已通过国家三级等保认证。但各个企业租户使用宜搭生产或搭建出来的应用，默认是不具备三级等保标准的，需要自行联系第三方进行单独认证申请（需要额外费用）。

