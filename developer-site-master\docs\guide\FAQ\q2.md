---
title: 如何在自定义页面使用 Apache ECharts？
order: 2
---

# 如何在自定义页面使用 Apache ECharts？

## 使用场景

有些情况下我们希望在宜搭的自定义页面中使用宜搭组件库中没有自带组件，例如 ECharts 图表组件，开发者可以通过在自定义页面引入 ECharts.js，实现宜搭内部数据的可视化展示的案例，来介绍如何在自定义页面引入外部 JS,实现复杂场景需求。

## 视频教程

<video width="100%" controls>
  <source src="https://cloud.video.taobao.com/play/u/null/p/1/e/6/t/1/d/ud/366302299291.mp4" type="video/mp4"></source>
</video>

## 操作步骤

### 步骤 1：配置 JSX 组件相关属性

创建一个自定义页面并在组件库中拖动一个 `JSX` 组件到页面中间画布，设置 id 属性和样式。
操作方法：

1. 页面点击选择 jsx 组件，在右侧属性栏中，点击编辑 JSX 代码按钮;
2. 在 div 后添加 id="main"字段;
3. 点击右侧样式栏，点击源码编辑，输入`#main { height：400px }` 字段;

如下所示：
![](https://img.alicdn.com/imgextra/i1/O1CN01l93zZI1bMbYrxXyBu_!!6000000003451-2-tps-1439-717.png_.webp)

### 步骤 2：绑定 didmount 生命周期加载远程 JS 资源

我们可以在页面的 didMount 生命周期中加载 cdn 上的 js 资源（关于自定义页面的生命周期详见[文档](/docs/guide/concept/lifecycle.md)），我们可以将以下代码直接复制到自定义页面动作面板的 didMount 内，如下所示：

![](https://img.alicdn.com/imgextra/i4/O1CN01DpT94L1GNsjZVL3sy_!!6000000000611-2-tps-3582-2018.png_.webp)

loadScript 方法的代码如下所示：

```js
//loadScript方法，创建一个script标签并引入外部JS，执行成功后配合代码快速开发
export function loadScript() {
  //创建script标签并引入echarts.js
  ////当异步代码执行成功时，我们才会调用resolve(...), 当异步代码失败时就会调用reject(...)
  new Promise((resolve, reject) => {
    const src = 'https://g.alicdn.com/code/lib/echarts/5.3.0/echarts.min.js';
    const script = document.createElement('script');
    script.setAttribute('type', 'text/javascript');
    script.src = src;
    document.body.appendChild(script);
    //script标签的onload事件都是在外部js文件被加载完成并执行完成后才被触发的。
    script.onload = () => {
      resolve('成功');
    };
    script.onerror = reject;
  }).then(() => {
    //引用成功后加载雷达图js
    radar();
  });
}
```

### 步骤 3：利用 ECharts 实现一个雷达图渲染方案

通过上面的 js 实现可以看到，我们在加载完远程 JS 资源后会执行一个`radar`函数来渲染雷达图，我们在动作面板中增加一个 radar 函数用于将雷达图渲染到我们在步骤 1 中创建的 jsx 组件中，具体雷达图的使用方式请参考[ECharts 文档](https://echarts.apache.org/zh/index.html)；
![](https://img.alicdn.com/imgextra/i4/O1CN01HKnJ0P1CWnzIb31KN_!!6000000000089-2-tps-3582-2020.png_.webp)

radar 方法的代码如下所示：

```js
//雷达图js方法
export function radar() {
  //雷达图js

  var chartDom = document.getElementById('main');
  chartDom.style.height = '400px';
  //为ECharts准备一个具备大小（宽高）的Dom，在JSX组件的样式中自定义设置宽高度
  // 基于准备好的dom，初始化echarts实例
  var myChart = echarts.init(chartDom);
  // 指定图表的配置项和数据
  var option;
  option = {
    //data使用了变量数据源，也可以直接写入
    title: {
      text: '员工综合能力',
    },
    legend: {
      data: state.nameArr,
      // data: ["宜搭-ly"]
    },
    radar: {
      // shape: 'circle',
      indicator: [
        { name: '业绩绩效', max: 100 },
        { name: '综合能力', max: 100 },
        { name: '反应能力', max: 100 },
        { name: '专业能力', max: 100 },
        { name: '判断能力', max: 100 },
        { name: '服务能力', max: 100 },
      ],
    },
    series: [
      {
        name: '员工综合能力',
        type: 'radar',
        data: state.dataArr,
        // data: [
        //   {
        //     value: [80, 85, 90, 90, 88, 92],
        //     name: '宜搭-ly'
        //   }
        // ]
      },
    ],
  };
  // 使用刚指定的配置项和数据显示图表。
  option && myChart.setOption(option);
}
```

### 步骤 3：绑定数据变量

通过上面 radar 的方法，开发者可以看到，我们读取了 state 上的两个变量分别为 `nameArr` 和 `dataArr`，因此我们需要设置雷达图的数据变量，如下所示：
![](https://img.alicdn.com/imgextra/i3/O1CN01l23bJA1cAWjd9CKLP_!!6000000003560-2-tps-1340-808.png_.webp)

## 展示效果

用户可以访问官方提供的[引入 ECharts 示例](https://docs.aliwork.com/docs/yida_subject/_1/cv1rpl1oi5eqlea9)来查看效果及具体实现，最终展示效果如下所示：

![](https://img.alicdn.com/imgextra/i3/O1CN01aSIyUz1X433ES1i28_!!6000000002869-2-tps-480-428.png_.webp)
