---
title: CheckboxField 多选
order: 4
---

# CheckboxField 多选

## 何时使用

- 在一组可选项中进行多项选择时。
- 单独使用可以表示两种状态之间的切换，和 switch 类似。区别在于切换 switch 会直接触发状态改变，而 checkbox 一般用于状态标记，需要和提交操作配合。

## 组件示例

import Iframe from 'components/Iframe';

<Iframe url="https://www.aliwork.com/developer/check-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  category="form"
  dataSource={[
    {
      code: 'value',
      type: 'string[]',
      default: '-',
      desc: '当前组件默认值',
    },
    {
      code: 'itemDirection',
      type: `'hoz' | 'ver'`,
      default: `'hoz'`,
      desc: 'PC端选项排列方式',
    },
    {
      code: 'useDrawerInMobile',
      type: 'boolean',
      default: 'false',
      desc: '手机端排列方式，为true则直接平铺，为false则底部弹层',
    },
    {
      code: 'iconPosition',
      type: `'left' | 'right'`,
      default: `'left'`,
      desc: '手机端Icon位置',
    },
    {
      code: 'dataSource',
      type: '[DataSource[]](/docs/components/interface#datasource)',
      default: `~~~json
[
  {
    "text": "选项一",
    "value": "1"
  },
  {
    "text": "选项二",
    "value": "2"
  },
  {
    "text": "选项三",
    "value": "3"
  }
]
      `,
      desc: '设置选项',
    },
    {
      code: 'onChange',
      type: '(value: string[]) => void',
      default: '',
      desc: '组件值发生改变事件',
    },
  ]}
/>
