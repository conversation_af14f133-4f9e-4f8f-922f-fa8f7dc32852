---
title: Tree 树形控件
order: 10
---

# Tree 树形控件

## 何时使用

- 适用于大量、具有层级关系的数据展示场景中，并且利用组件的展开收起和关联选中等交互可以方便地对数据进行操作处理。

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/tree-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'dataSource',
      type: '[TreeDataSource[]](/docs/components/interface#treedatasource)',
      default: `~~~json
[
  {
    key: "0-0",
    label: "0-0",
    children: [
      {
        key: "0-0-0",
        label: "0-0-0",
        children: [
          {
            key: "0-0-0-0",
            label: "0-0-0-0",
            children: [
              {
                key: "0-0-0-0-0",
                label: "0-0-0-0-0",
              },
            ],
          },
          {
            key: "0-0-0-1",
            label: "0-0-0-1",
          },
        ],
      },
      {
        key: "0-0-1",
        label: "0-0-1",
        children: [
          {
            key: "0-0-1-0",
            label: "0-0-1-0",
          },
          {
            key: "0-0-1-1",
            label: "0-0-1-1",
          },
        ],
      },
    ],
  },
]`,
      desc: '节点数据',
    },
    {
      code: 'processDataSource',
      type: '(data: any) => TreeDataSource[]',
      default: '-',
      desc: '数据预处理函数',
    },
    {
      code: 'showLine',
      type: 'boolean',
      default: 'true',
      desc: '是否显示节点连线',
    },
    {
      code: 'multiple',
      type: 'boolean',
      default: 'false',
      desc: '是否支持多选',
    },
    {
      code: 'editable',
      type: 'boolean',
      default: 'false',
      desc: '是否支持编辑',
    },
    {
      code: 'draggable',
      type: 'boolean',
      default: 'false',
      desc: '是否支持拖拽',
    },
    {
      code: 'defaultExpandAll',
      type: 'boolean',
      default: 'true',
      desc: '默认展开所有节点',
    },
    {
      code: 'defaultExpandedKeys',
      type: 'string[]',
      default: `['0-0-0']`,
      desc: '默认展开的节点',
    },
    {
      code: 'selectable',
      type: 'boolean',
      default: 'true',
      desc: '是否支持选中',
    },
    {
      code: 'checkedKeys',
      type: 'string[]',
      default: `['0-0-0']`,
      desc: '默认选中节点',
    },
    {
      code: 'checkable',
      type: 'boolean',
      default: 'false',
      desc: '是否显示复选框',
    },
    {
      code: 'checkedKeys',
      type: 'string[]',
      default: `["0-0-0"]`,
      desc: '默认勾选节点',
    },
    {
      code: 'checkStrictly',
      type: 'boolean',
      default: 'false',
      desc: '复选框完全受控',
    },
    {
      code: 'checkStrictly',
      type: `'all' | 'parent' | 'child' `,
      default: ` 'parent' `,
      desc: '定义选中时回填的方式，all：返回所有选中节点、parent：父节点都选中时只返回父节点、child：父节点都选中时只返回子节点',
    },
    {
      code: 'isLoadData',
      type: 'boolean',
      default: 'false',
      desc: '是否开启异步加载',
    },
    {
      code: 'loadData',
      type: '(data: TreeDataSource) => Promise<TreeDataSource[]>',
      default: '-',
      desc: '异步加载时触发的事件',
    },
    {
      code: 'onSelect',
      type: '(selectedKeys: string[], extra: any) => void',
      default: '-',
      desc: '选中或取消选中节点时触发的事件',
    },
    {
      code: 'onCheck',
      type: '(checkedKeys: string[], extra: any) => void',
      default: '-',
      desc: '勾选或取消勾选复选框时触发的事件',
    },
    {
      code: 'onExpand',
      type: '(expandedKeys: string[], extra: any) => void',
      default: '-',
      desc: '展开收起节点时触发的事件',
    },
    {
      code: 'onEditFinish',
      type: '(key: string, label: string, node: TreeDataSource) => void',
      default: '-',
      desc: '编辑节点内容完成时触发的事件',
    },
    {
      code: 'onDragStart',
      type: '(info: any) => void',
      default: '-',
      desc: '开始拖拽节点时触发的事件',
    },
    {
      code: 'onDragEnter',
      type: '(info: any) => void',
      default: '-',
      desc: '拖拽节点进入目标时触发的事件',
    },
    {
      code: 'onDragOver',
      type: '(info: any) => void',
      default: '-',
      desc: '拖拽节点在目标节点上移动时触发的事件',
    },
    {
      code: 'onDragLeave',
      type: '(info: any) => void',
      default: '-',
      desc: '拖拽节点离开目标节点时触发的事件',
    },
    {
      code: 'onDragEnd',
      type: '(info: any) => void',
      default: '-',
      desc: '拖拽结束时触发的事件',
    },
    {
      code: 'onDrop',
      type: '(info: any) => void',
      default: '-',
      desc: '	拖拽节点放入目标节点内或前后触发的事件',
    },
    {
      code: 'canDrop',
      type: '(info: any) => boolean',
      default: '-',
      desc: '节点是否可被作为目标节点时触发的事件',
    },
    {
      code: 'selectedKeys',
      type: 'string[]',
      default: `['0-0-0']`,
      desc: '默认选中节点',
    },
    {
      code: 'expandedKeys',
      type: 'string[]',
      default: `['0-0-0']`,
      desc: '展开的节点',
    },
    {
      code: 'checkStrictly',
      type: 'boolean',
      default: 'false',
      desc: '复选框完全受控',
    },
  ]}
/>
