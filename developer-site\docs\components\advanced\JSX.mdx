---
title: JSX
order: 1
---

# JSX

JSX 组件允许你通过 jsx 代码渲染一个完全自定义的组件，一般用于纯展示的场景。

## 何时使用

- 用于通过 JSX 形式来自定义渲染内容。

## 组件示例

import Iframe from 'components/Iframe';

<Iframe url="https://www.aliwork.com/developer/jsx-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'render',
      type: '() => ReactNode',
      default: '-',
      desc: '展示内容，可直接返回JSX代码',
    },
  ]}
/>
