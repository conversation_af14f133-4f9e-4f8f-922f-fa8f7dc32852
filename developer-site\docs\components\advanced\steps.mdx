---
title: Steps 步骤条
order: 15
---

# Steps 步骤条

引导用户按照流程完成任务的导航条。

## 何时使用

- 当任务复杂或者存在先后关系时，将其分解成一系列步骤，从而简化任务。

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/steps-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'dataSource',
      type: '[StepsDataSource[]](/docs/components/interface#stepsdatasource)',
      default: `~~~json
[
  {
    "title": "step1",
    "content": "Open the refrigerator door"
  },
  {
    "title": "step2",
    "content": "Put the elephant in the refrigerator"
  },
  {
    "title": "step3",
    "content": "Close the refrigerator door"
  }
]
~~~`,
      desc: '步骤数据',
    },
    {
      code: 'current',
      type: 'number',
      default: '1',
      desc: '当前步骤',
    },
    {
      code: 'shape',
      type: `'circle' | 'arrow' | 'dot'`,
      default: `'circle'`,
      desc: '步骤条展示类型',
    },
    {
      code: 'direction',
      type: `'horizontal' | 'vertical'`,
      default: `'horizontal'`,
      desc: '步骤条展示方向',
    },
    {
      code: 'labelPlacement',
      type: `'horizontal' | 'vertical'`,
      default: `'vertical'`,
      desc: '内容排列方式',
    },
    {
      code: 'readOnly',
      type: 'boolean',
      default: 'false',
      desc: '是否只读模式',
    },
    {
      code: 'animation',
      type: 'boolean',
      default: 'true',
      desc: '是否开启动效',
    },
    {
      code: 'contentRender',
      type: '(item: StepsDataSource, index: number)=> ReactNode',
      default: '-',
      desc: '内容自定义渲染',
    },
    {
      code: 'onClick',
      type: '(index: number) => void',
      default: '-',
      desc: '点击步骤时触发事件',
    },
  ]}
/>